package config

import (
	"fmt"
	"os"
	"strconv"
	"time"

	"github.com/joho/godotenv"
)

// Config 应用程序配置结构
type Config struct {
	Server   ServerConfig   `json:"server"`
	Database DatabaseConfig `json:"database"`
	Redis    RedisConfig    `json:"redis"`
	Auth     AuthConfig     `json:"auth"`
	AI       AIConfig       `json:"ai"`
	Game     GameConfig     `json:"game"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port         string        `json:"port"`
	Host         string        `json:"host"`
	ReadTimeout  time.Duration `json:"read_timeout"`
	WriteTimeout time.Duration `json:"write_timeout"`
	IdleTimeout  time.Duration `json:"idle_timeout"`
	Environment  string        `json:"environment"` // development, production, test
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host         string        `json:"host"`
	Port         int           `json:"port"`
	User         string        `json:"user"`
	Password     string        `json:"password"`
	DBName       string        `json:"db_name"`
	SSLMode      string        `json:"ssl_mode"`
	MaxOpenConns int           `json:"max_open_conns"`
	MaxIdleConns int           `json:"max_idle_conns"`
	MaxLifetime  time.Duration `json:"max_lifetime"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host         string `json:"host"`
	Port         int    `json:"port"`
	Password     string `json:"password"`
	DB           int    `json:"db"`
	PoolSize     int    `json:"pool_size"`
	MinIdleConns int    `json:"min_idle_conns"`
}

// AuthConfig 认证配置
type AuthConfig struct {
	JWTSecret     string        `json:"jwt_secret"`
	JWTExpiration time.Duration `json:"jwt_expiration"`
	OAuth         OAuthConfig   `json:"oauth"`
}

// OAuthConfig OAuth配置
type OAuthConfig struct {
	Providers map[string]OAuthProvider `json:"providers"`
}

// OAuthProvider OAuth提供商配置
type OAuthProvider struct {
	ClientID     string   `json:"client_id"`
	ClientSecret string   `json:"client_secret"`
	RedirectURL  string   `json:"redirect_url"`
	Scopes       []string `json:"scopes"`
	AuthURL      string   `json:"auth_url"`
	TokenURL     string   `json:"token_url"`
	UserInfoURL  string   `json:"user_info_url"`
}

// AIConfig AI服务配置
type AIConfig struct {
	BaseURL      string        `json:"base_url"`
	Token        string        `json:"token"`
	Timeout      time.Duration `json:"timeout"`
	MaxRetries   int           `json:"max_retries"`
	RetryDelay   time.Duration `json:"retry_delay"`
	QueueSize    int           `json:"queue_size"`
	BatchSize    int           `json:"batch_size"`
	BatchTimeout time.Duration `json:"batch_timeout"`
	MockEnabled  bool          `json:"mock_enabled"` // 测试时启用mock
}

// GameConfig 游戏配置
type GameConfig struct {
	MaxWorldsPerUser     int           `json:"max_worlds_per_user"`
	MaxPlayersPerWorld   int           `json:"max_players_per_world"`
	DefaultTimeRate      float64       `json:"default_time_rate"`
	TickInterval         time.Duration `json:"tick_interval"`
	MaxMemoryPerChar     int           `json:"max_memory_per_char"`
	MaxExperiencePerChar int           `json:"max_experience_per_char"`
}

// Load 加载配置
func Load() (*Config, error) {
	// 加载.env文件（如果存在）
	_ = godotenv.Load()

	config := &Config{
		Server: ServerConfig{
			Port:         getEnv("SERVER_PORT", "8080"),
			Host:         getEnv("SERVER_HOST", "0.0.0.0"),
			ReadTimeout:  getDurationEnv("SERVER_READ_TIMEOUT", 30*time.Second),
			WriteTimeout: getDurationEnv("SERVER_WRITE_TIMEOUT", 30*time.Second),
			IdleTimeout:  getDurationEnv("SERVER_IDLE_TIMEOUT", 120*time.Second),
			Environment:  getEnv("ENVIRONMENT", "development"),
		},
		Database: DatabaseConfig{
			Host:         getEnv("DB_HOST", "localhost"),
			Port:         getIntEnv("DB_PORT", 5432),
			User:         getEnv("DB_USER", "postgres"),
			Password:     getEnv("DB_PASSWORD", ""),
			DBName:       getEnv("DB_NAME", "ai_text_game"),
			SSLMode:      getEnv("DB_SSL_MODE", "disable"),
			MaxOpenConns: getIntEnv("DB_MAX_OPEN_CONNS", 25),
			MaxIdleConns: getIntEnv("DB_MAX_IDLE_CONNS", 5),
			MaxLifetime:  getDurationEnv("DB_MAX_LIFETIME", 5*time.Minute),
		},
		Redis: RedisConfig{
			Host:         getEnv("REDIS_HOST", "localhost"),
			Port:         getIntEnv("REDIS_PORT", 6379),
			Password:     getEnv("REDIS_PASSWORD", ""),
			DB:           getIntEnv("REDIS_DB", 0),
			PoolSize:     getIntEnv("REDIS_POOL_SIZE", 10),
			MinIdleConns: getIntEnv("REDIS_MIN_IDLE_CONNS", 2),
		},
		Auth: AuthConfig{
			JWTSecret:     getEnv("JWT_SECRET", "your-secret-key"),
			JWTExpiration: getDurationEnv("JWT_EXPIRATION", 24*time.Hour),
			OAuth: OAuthConfig{
				Providers: make(map[string]OAuthProvider),
			},
		},
		AI: AIConfig{
			BaseURL:      getEnv("AI_BASE_URL", "https://wm.atjog.com"),
			Token:        getEnv("AI_TOKEN", ""),
			Timeout:      getDurationEnv("AI_TIMEOUT", 30*time.Second),
			MaxRetries:   getIntEnv("AI_MAX_RETRIES", 3),
			RetryDelay:   getDurationEnv("AI_RETRY_DELAY", 1*time.Second),
			QueueSize:    getIntEnv("AI_QUEUE_SIZE", 1000),
			BatchSize:    getIntEnv("AI_BATCH_SIZE", 10),
			BatchTimeout: getDurationEnv("AI_BATCH_TIMEOUT", 5*time.Second),
			MockEnabled:  getBoolEnv("AI_MOCK_ENABLED", true), // 默认启用mock用于测试
		},
		Game: GameConfig{
			MaxWorldsPerUser:     getIntEnv("GAME_MAX_WORLDS_PER_USER", 10),
			MaxPlayersPerWorld:   getIntEnv("GAME_MAX_PLAYERS_PER_WORLD", 10),
			DefaultTimeRate:      getFloatEnv("GAME_DEFAULT_TIME_RATE", 1.0),
			TickInterval:         getDurationEnv("GAME_TICK_INTERVAL", 30*time.Second),
			MaxMemoryPerChar:     getIntEnv("GAME_MAX_MEMORY_PER_CHAR", 100),
			MaxExperiencePerChar: getIntEnv("GAME_MAX_EXPERIENCE_PER_CHAR", 1000),
		},
	}

	// 加载OAuth提供商配置
	config.loadOAuthProviders()

	return config, nil
}

// loadOAuthProviders 加载OAuth提供商配置
func (c *Config) loadOAuthProviders() {
	// Google OAuth配置
	if clientID := getEnv("OAUTH_GOOGLE_CLIENT_ID", ""); clientID != "" {
		c.Auth.OAuth.Providers["google"] = OAuthProvider{
			ClientID:     clientID,
			ClientSecret: getEnv("OAUTH_GOOGLE_CLIENT_SECRET", ""),
			RedirectURL:  getEnv("OAUTH_GOOGLE_REDIRECT_URL", ""),
			Scopes:       []string{"openid", "profile", "email"},
			AuthURL:      "https://accounts.google.com/o/oauth2/auth",
			TokenURL:     "https://oauth2.googleapis.com/token",
			UserInfoURL:  "https://www.googleapis.com/oauth2/v2/userinfo",
		}
	}

	// GitHub OAuth配置
	if clientID := getEnv("OAUTH_GITHUB_CLIENT_ID", ""); clientID != "" {
		c.Auth.OAuth.Providers["github"] = OAuthProvider{
			ClientID:     clientID,
			ClientSecret: getEnv("OAUTH_GITHUB_CLIENT_SECRET", ""),
			RedirectURL:  getEnv("OAUTH_GITHUB_REDIRECT_URL", ""),
			Scopes:       []string{"user:email"},
			AuthURL:      "https://github.com/login/oauth/authorize",
			TokenURL:     "https://github.com/login/oauth/access_token",
			UserInfoURL:  "https://api.github.com/user",
		}
	}
}

// DSN 返回数据库连接字符串
func (d *DatabaseConfig) DSN() string {
	return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		d.Host, d.Port, d.User, d.Password, d.DBName, d.SSLMode)
}

// 辅助函数

// getEnvWithPrefix 获取带前缀的环境变量，支持多个前缀优先级
// 优先级: 1. 带前缀的环境变量 2. 普通环境变量 3. 默认值
func getEnvWithPrefix(key, defaultValue string, prefixes ...string) string {
	// 首先尝试带前缀的环境变量
	for _, prefix := range prefixes {
		prefixedKey := prefix + key
		if value := os.Getenv(prefixedKey); value != "" {
			return value
		}
	}

	// 然后尝试普通环境变量
	if value := os.Getenv(key); value != "" {
		return value
	}

	// 最后返回默认值
	return defaultValue
}

// getEnv 获取环境变量，支持项目前缀
func getEnv(key, defaultValue string) string {
	return getEnvWithPrefix(key, defaultValue, "AI_TEXT_GAME_", "AITEXTGAME_")
}

func getIntEnv(key string, defaultValue int) int {
	value := getEnv(key, "")
	if value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getFloatEnv(key string, defaultValue float64) float64 {
	value := getEnv(key, "")
	if value != "" {
		if floatValue, err := strconv.ParseFloat(value, 64); err == nil {
			return floatValue
		}
	}
	return defaultValue
}

func getBoolEnv(key string, defaultValue bool) bool {
	value := getEnv(key, "")
	if value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

func getDurationEnv(key string, defaultValue time.Duration) time.Duration {
	value := getEnv(key, "")
	if value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	return defaultValue
}
