package handlers

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"ai-text-game-iam-npc/internal/auth"
	"ai-text-game-iam-npc/internal/game"
	"ai-text-game-iam-npc/internal/models"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// GameHandler 游戏处理器
type GameHandler struct {
	worldService     *game.WorldService
	characterService *game.CharacterService
	sceneService     *game.SceneService
	eventService     *game.EventService
	stateService     *game.StateService
}

// NewGameHandler 创建游戏处理器
func NewGameHandler(
	worldService *game.WorldService,
	characterService *game.CharacterService,
	sceneService *game.SceneService,
	eventService *game.EventService,
	stateService *game.StateService,
) *GameHandler {
	return &GameHandler{
		worldService:     worldService,
		characterService: characterService,
		sceneService:     sceneService,
		eventService:     eventService,
		stateService:     stateService,
	}
}

// CreateWorld 创建世界
// @Summary 创建新世界
// @Description 创建一个新的游戏世界
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body CreateWorldRequest true "创建世界请求"
// @Success 200 {object} Response{data=models.World}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /game/worlds [post]
func (h *GameHandler) CreateWorld(c *gin.Context) {
	var req CreateWorldRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	// 设置默认配置
	if req.Config == nil {
		req.Config = make(map[string]interface{})
	}

	world, err := h.worldService.CreateWorld(c.Request.Context(), userID, req.Name, req.Description, req.Config)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "创建世界失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "创建世界成功",
		Data:    world,
	})
}

// GetWorld 获取世界信息
// @Summary 获取世界信息
// @Description 获取指定世界的详细信息
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param world_id path string true "世界ID"
// @Success 200 {object} Response{data=models.World}
// @Failure 400 {object} Response
// @Failure 404 {object} Response
// @Router /game/worlds/{world_id} [get]
func (h *GameHandler) GetWorld(c *gin.Context) {
	worldIDStr := c.Param("world_id")
	worldID, err := uuid.Parse(worldIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的世界ID格式",
		})
		return
	}

	world, err := h.worldService.GetWorld(c.Request.Context(), worldID)
	if err != nil {
		if err.Error() == "世界不存在" {
			c.JSON(http.StatusNotFound, Response{
				Success: false,
				Message: "世界不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取世界信息失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取世界信息成功",
		Data:    world,
	})
}

// GetMyWorlds 获取我的世界列表
// @Summary 获取我的世界列表
// @Description 获取当前用户创建的世界列表
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(20)
// @Success 200 {object} Response{data=PaginatedResponse}
// @Failure 401 {object} Response
// @Router /game/my-worlds [get]
func (h *GameHandler) GetMyWorlds(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}
	offset := (page - 1) * limit

	worlds, total, err := h.worldService.GetWorldsByCreator(c.Request.Context(), userID, limit, offset)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取世界列表失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取世界列表成功",
		Data: PaginatedResponse{
			Items:      worlds,
			Total:      total,
			Page:       page,
			Limit:      limit,
			TotalPages: (total + int64(limit) - 1) / int64(limit),
		},
	})
}

// GetPublicWorlds 获取公开世界列表
// @Summary 获取公开世界列表
// @Description 获取所有公开的世界列表
// @Tags Game
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(20)
// @Success 200 {object} Response{data=PaginatedResponse}
// @Router /game/public-worlds [get]
func (h *GameHandler) GetPublicWorlds(c *gin.Context) {
	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}
	offset := (page - 1) * limit

	worlds, total, err := h.worldService.GetPublicWorlds(c.Request.Context(), limit, offset)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取公开世界列表失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取公开世界列表成功",
		Data: PaginatedResponse{
			Items:      worlds,
			Total:      total,
			Page:       page,
			Limit:      limit,
			TotalPages: (total + int64(limit) - 1) / int64(limit),
		},
	})
}

// JoinWorld 加入世界
// @Summary 加入世界
// @Description 用户加入指定的世界
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param world_id path string true "世界ID"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /game/worlds/{world_id}/join [post]
func (h *GameHandler) JoinWorld(c *gin.Context) {
	worldIDStr := c.Param("world_id")
	worldID, err := uuid.Parse(worldIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的世界ID格式",
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	if err := h.worldService.JoinWorld(c.Request.Context(), worldID, userID); err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "加入世界失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "成功加入世界",
	})
}

// LeaveWorld 离开世界
// @Summary 离开世界
// @Description 用户离开指定的世界
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param world_id path string true "世界ID"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /game/worlds/{world_id}/leave [post]
func (h *GameHandler) LeaveWorld(c *gin.Context) {
	worldIDStr := c.Param("world_id")
	worldID, err := uuid.Parse(worldIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的世界ID格式",
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	if err := h.worldService.LeaveWorld(c.Request.Context(), worldID, userID); err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "离开世界失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "成功离开世界",
	})
}

// CreateCharacter 创建角色
// @Summary 创建角色
// @Description 在指定世界中创建新角色
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body CreateCharacterRequest true "创建角色请求"
// @Success 200 {object} Response{data=models.Character}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /game/characters [post]
func (h *GameHandler) CreateCharacter(c *gin.Context) {
	var req CreateCharacterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	character, err := h.characterService.CreateCharacter(
		c.Request.Context(),
		req.WorldID,
		&userID,
		req.Name,
		req.Description,
		req.CharacterType,
		req.Traits,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "创建角色失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "创建角色成功",
		Data:    character,
	})
}

// GetCharacter 获取角色信息
// @Summary 获取角色信息
// @Description 获取指定角色的详细信息
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param character_id path string true "角色ID"
// @Success 200 {object} Response{data=models.Character}
// @Failure 400 {object} Response
// @Failure 404 {object} Response
// @Router /game/characters/{character_id} [get]
func (h *GameHandler) GetCharacter(c *gin.Context) {
	characterIDStr := c.Param("character_id")
	characterID, err := uuid.Parse(characterIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的角色ID格式",
		})
		return
	}

	character, err := h.characterService.GetCharacter(c.Request.Context(), characterID)
	if err != nil {
		if err.Error() == "角色不存在" {
			c.JSON(http.StatusNotFound, Response{
				Success: false,
				Message: "角色不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取角色信息失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取角色信息成功",
		Data:    character,
	})
}

// GetMyCharacters 获取我的角色列表
// @Summary 获取我的角色列表
// @Description 获取当前用户的角色列表
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(20)
// @Success 200 {object} Response{data=PaginatedResponse}
// @Failure 401 {object} Response
// @Router /game/my-characters [get]
func (h *GameHandler) GetMyCharacters(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}
	offset := (page - 1) * limit

	characters, total, err := h.characterService.GetCharactersByUser(c.Request.Context(), userID, limit, offset)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取角色列表失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取角色列表成功",
		Data: PaginatedResponse{
			Items:      characters,
			Total:      total,
			Page:       page,
			Limit:      limit,
			TotalPages: (total + int64(limit) - 1) / int64(limit),
		},
	})
}

// GetWorldCharacters 获取世界中的角色列表
// @Summary 获取世界中的角色列表
// @Description 获取指定世界中的角色列表，支持按角色类型筛选
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param world_id path string true "世界ID"
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(20)
// @Param character_type query string false "角色类型" Enums(player, npc)
// @Success 200 {object} Response{data=PaginatedResponse}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /game/worlds/{world_id}/characters [get]
func (h *GameHandler) GetWorldCharacters(c *gin.Context) {
	worldIDStr := c.Param("world_id")
	worldID, err := uuid.Parse(worldIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的世界ID格式",
		})
		return
	}

	// 获取当前用户ID
	_, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	characterType := c.Query("character_type")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}
	offset := (page - 1) * limit

	// 验证角色类型参数
	if characterType != "" && characterType != "player" && characterType != "npc" {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的角色类型，只支持 player 或 npc",
		})
		return
	}

	// 首先检查世界是否存在
	_, err = h.worldService.GetWorld(c.Request.Context(), worldID)
	if err != nil {
		if err.Error() == "世界不存在" {
			c.JSON(http.StatusNotFound, Response{
				Success: false,
				Message: "世界不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取世界信息失败",
			Error:   err.Error(),
		})
		return
	}

	characters, total, err := h.characterService.GetCharactersByWorld(c.Request.Context(), worldID, characterType, limit, offset)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取世界角色列表失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取世界角色列表成功",
		Data: PaginatedResponse{
			Items:      characters,
			Total:      total,
			Page:       page,
			Limit:      limit,
			TotalPages: (total + int64(limit) - 1) / int64(limit),
		},
	})
}

// 请求结构体定义

// CreateWorldRequest 创建世界请求
type CreateWorldRequest struct {
	Name        string                 `json:"name" binding:"required"`
	Description string                 `json:"description"`
	Config      map[string]interface{} `json:"config"`
}

// CreateCharacterRequest 创建角色请求
type CreateCharacterRequest struct {
	WorldID       uuid.UUID `json:"world_id" binding:"required"`
	Name          string    `json:"name" binding:"required"`
	Description   string    `json:"description"`
	CharacterType string    `json:"character_type" binding:"required"`
	Traits        []string  `json:"traits"`
}

// UpdateCharacterRequest 更新角色请求
type UpdateCharacterRequest struct {
	Name          string   `json:"name"`
	Description   string   `json:"description"`
	Traits        []string `json:"traits"`
	CharacterType string   `json:"character_type"` // 添加角色类型字段
}

// AddTraitRequest 添加特质请求
type AddTraitRequest struct {
	Trait string `json:"trait" binding:"required"`
}

// AddMemoryRequest 添加记忆请求
type AddMemoryRequest struct {
	Type       string                 `json:"type" binding:"required"`
	Content    string                 `json:"content" binding:"required"`
	Importance int                    `json:"importance" binding:"min=1,max=10"`
	Clarity    float64                `json:"clarity" binding:"min=0,max=1"`
	Tags       []string               `json:"tags"`
	RelatedIDs []uuid.UUID            `json:"related_ids"`
	Metadata   map[string]interface{} `json:"metadata"`
}

// AddExperienceRequest 添加阅历请求
type AddExperienceRequest struct {
	Type     string `json:"type" binding:"required"`
	Category string `json:"category" binding:"required"`
}

// PerformActionRequest 执行行动请求
type PerformActionRequest struct {
	WorldID    uuid.UUID              `json:"world_id" binding:"required"`
	ActionType string                 `json:"action_type" binding:"required"`
	TargetType string                 `json:"target_type"`
	TargetID   string                 `json:"target_id"`
	Parameters map[string]interface{} `json:"parameters"`
}

// InteractionRequest 交互请求
type InteractionRequest struct {
	WorldID         uuid.UUID              `json:"world_id" binding:"required"`
	InteractionType string                 `json:"interaction_type" binding:"required"`
	Content         string                 `json:"content"`
	Parameters      map[string]interface{} `json:"parameters"`
}

// SpeakRequest 说话请求
type SpeakRequest struct {
	WorldID           uuid.UUID  `json:"world_id" binding:"required"`
	Content           string     `json:"content" binding:"required"`
	SpeechType        string     `json:"speech_type"`        // say, whisper, shout, think
	TargetCharacterID *uuid.UUID `json:"target_character_id"` // 私聊目标
	Volume            string     `json:"volume"`             // quiet, normal, loud
	Emotion           string     `json:"emotion"`            // happy, sad, angry, neutral
}

// TriggerEventRequest 触发事件请求
type TriggerEventRequest struct {
	WorldID            uuid.UUID              `json:"world_id" binding:"required"`
	EventType          string                 `json:"event_type" binding:"required"`
	Name               string                 `json:"name" binding:"required"`
	Description        string                 `json:"description"`
	Priority           int                    `json:"priority"`
	Participants       []uuid.UUID            `json:"participants"`
	EventData          map[string]interface{} `json:"event_data"`
	ProcessImmediately bool                   `json:"process_immediately"`
}

// ActionResult 行动结果
type ActionResult struct {
	EventID     uuid.UUID              `json:"event_id"`
	ActionType  string                 `json:"action_type"`
	Success     bool                   `json:"success"`
	Message     string                 `json:"message"`
	Effects     map[string]interface{} `json:"effects"`
	Timestamp   time.Time              `json:"timestamp"`
}

// InteractionResult 交互结果
type InteractionResult struct {
	EventID         uuid.UUID              `json:"event_id"`
	InteractionType string                 `json:"interaction_type"`
	Success         bool                   `json:"success"`
	Message         string                 `json:"message"`
	Response        string                 `json:"response"`
	Effects         map[string]interface{} `json:"effects"`
	Timestamp       time.Time              `json:"timestamp"`
}

// SpeechResult 说话结果
type SpeechResult struct {
	EventID     uuid.UUID     `json:"event_id"`
	Content     string        `json:"content"`
	SpeechType  string        `json:"speech_type"`
	Volume      string        `json:"volume"`
	Emotion     string        `json:"emotion"`
	Listeners   []uuid.UUID   `json:"listeners"`
	Success     bool          `json:"success"`
	Message     string        `json:"message"`
	Timestamp   time.Time     `json:"timestamp"`
}

// EventResult 事件结果
type EventResult struct {
	EventID     uuid.UUID              `json:"event_id"`
	EventType   string                 `json:"event_type"`
	Name        string                 `json:"name"`
	Status      string                 `json:"status"`
	Success     bool                   `json:"success"`
	Message     string                 `json:"message"`
	Effects     map[string]interface{} `json:"effects"`
	Timestamp   time.Time              `json:"timestamp"`
}

// UpdateTimeRequest 更新时间请求
type UpdateTimeRequest struct {
	Minutes int64 `json:"minutes" binding:"required,min=1"`
}

// UpdateCharacter 更新角色信息
// @Summary 更新角色信息
// @Description 更新指定角色的基本信息
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param character_id path string true "角色ID"
// @Param request body UpdateCharacterRequest true "更新角色请求"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 403 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /game/characters/{character_id} [put]
func (h *GameHandler) UpdateCharacter(c *gin.Context) {
	characterIDStr := c.Param("character_id")
	characterID, err := uuid.Parse(characterIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的角色ID格式",
		})
		return
	}

	var req UpdateCharacterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	err = h.characterService.UpdateCharacter(c.Request.Context(), characterID, req.Name, req.Description, req.Traits, req.CharacterType, &userID)
	if err != nil {
		if err.Error() == "角色不存在或无权限操作" {
			c.JSON(http.StatusForbidden, Response{
				Success: false,
				Message: "角色不存在或无权限操作",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "更新角色失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "更新角色成功",
	})
}

// DeleteCharacter 删除角色
// @Summary 删除角色
// @Description 删除指定的角色
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param character_id path string true "角色ID"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 403 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /game/characters/{character_id} [delete]
func (h *GameHandler) DeleteCharacter(c *gin.Context) {
	characterIDStr := c.Param("character_id")
	characterID, err := uuid.Parse(characterIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的角色ID格式",
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	err = h.characterService.DeleteCharacter(c.Request.Context(), characterID, &userID)
	if err != nil {
		if err.Error() == "角色不存在或无权限操作" {
			c.JSON(http.StatusForbidden, Response{
				Success: false,
				Message: "角色不存在或无权限操作",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "删除角色失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "删除角色成功",
	})
}

// AddCharacterTrait 添加角色特质
// @Summary 添加角色特质
// @Description 为指定角色添加新的特质
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param character_id path string true "角色ID"
// @Param request body AddTraitRequest true "添加特质请求"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 403 {object} Response
// @Failure 500 {object} Response
// @Router /game/characters/{character_id}/traits [post]
func (h *GameHandler) AddCharacterTrait(c *gin.Context) {
	characterIDStr := c.Param("character_id")
	characterID, err := uuid.Parse(characterIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的角色ID格式",
		})
		return
	}

	var req AddTraitRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	err = h.characterService.AddCharacterTrait(c.Request.Context(), characterID, req.Trait, &userID)
	if err != nil {
		if err.Error() == "角色不存在或无权限操作" {
			c.JSON(http.StatusForbidden, Response{
				Success: false,
				Message: "角色不存在或无权限操作",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "添加特质失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "添加特质成功",
	})
}

// AddCharacterMemory 添加角色记忆
// @Summary 添加角色记忆
// @Description 为指定角色添加新的记忆
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param character_id path string true "角色ID"
// @Param request body AddMemoryRequest true "添加记忆请求"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 403 {object} Response
// @Failure 500 {object} Response
// @Router /game/characters/{character_id}/memories [post]
func (h *GameHandler) AddCharacterMemory(c *gin.Context) {
	characterIDStr := c.Param("character_id")
	characterID, err := uuid.Parse(characterIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的角色ID格式",
		})
		return
	}

	var req AddMemoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	memory := models.Memory{
		Type:       req.Type,
		Content:    req.Content,
		Importance: req.Importance,
		Clarity:    req.Clarity,
		Tags:       req.Tags,
		RelatedIDs: req.RelatedIDs,
		Metadata:   req.Metadata,
	}

	err = h.characterService.AddCharacterMemory(c.Request.Context(), characterID, memory, &userID)
	if err != nil {
		if err.Error() == "角色不存在或无权限操作" {
			c.JSON(http.StatusForbidden, Response{
				Success: false,
				Message: "角色不存在或无权限操作",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "添加记忆失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "添加记忆成功",
	})
}

// AddCharacterExperience 添加角色阅历
// @Summary 添加角色阅历
// @Description 为指定角色添加新的阅历
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param character_id path string true "角色ID"
// @Param request body AddExperienceRequest true "添加阅历请求"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 403 {object} Response
// @Failure 500 {object} Response
// @Router /game/characters/{character_id}/experiences [post]
func (h *GameHandler) AddCharacterExperience(c *gin.Context) {
	characterIDStr := c.Param("character_id")
	characterID, err := uuid.Parse(characterIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的角色ID格式",
		})
		return
	}

	var req AddExperienceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	err = h.characterService.AddCharacterExperience(c.Request.Context(), characterID, req.Type, req.Category, &userID)
	if err != nil {
		if err.Error() == "角色不存在或无权限操作" {
			c.JSON(http.StatusForbidden, Response{
				Success: false,
				Message: "角色不存在或无权限操作",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "添加阅历失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "添加阅历成功",
	})
}

// MoveCharacter 移动角色
// @Summary 移动角色
// @Description 移动角色到指定场景
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param character_id path string true "角色ID"
// @Param request body MoveCharacterRequest true "移动角色请求"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /game/characters/{character_id}/move [post]
func (h *GameHandler) MoveCharacter(c *gin.Context) {
	characterIDStr := c.Param("character_id")
	characterID, err := uuid.Parse(characterIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的角色ID格式",
		})
		return
	}

	var req MoveCharacterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	if err := h.characterService.MoveCharacter(c.Request.Context(), characterID, req.SceneID, &userID); err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "移动角色失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "角色移动成功",
	})
}

// CreateScene 创建场景
// @Summary 创建场景
// @Description 在指定世界中创建新场景
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body CreateSceneRequest true "创建场景请求"
// @Success 200 {object} Response{data=models.Scene}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /game/scenes [post]
func (h *GameHandler) CreateScene(c *gin.Context) {
	var req CreateSceneRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 设置默认属性
	if req.Properties == nil {
		req.Properties = make(map[string]interface{})
	}

	scene, err := h.sceneService.CreateScene(
		c.Request.Context(),
		req.WorldID,
		req.Name,
		req.Description,
		req.SceneType,
		req.Properties,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "创建场景失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "创建场景成功",
		Data:    scene,
	})
}

// GetScene 获取场景信息
// @Summary 获取场景信息
// @Description 获取指定场景的详细信息
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param scene_id path string true "场景ID"
// @Success 200 {object} Response{data=models.Scene}
// @Failure 400 {object} Response
// @Failure 404 {object} Response
// @Router /game/scenes/{scene_id} [get]
func (h *GameHandler) GetScene(c *gin.Context) {
	sceneIDStr := c.Param("scene_id")
	sceneID, err := uuid.Parse(sceneIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的场景ID格式",
		})
		return
	}

	scene, err := h.sceneService.GetScene(c.Request.Context(), sceneID)
	if err != nil {
		if err.Error() == "场景不存在" {
			c.JSON(http.StatusNotFound, Response{
				Success: false,
				Message: "场景不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取场景信息失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取场景信息成功",
		Data:    scene,
	})
}

// CreateEvent 创建事件
// @Summary 创建事件
// @Description 在指定世界中创建新事件
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body CreateEventRequest true "创建事件请求"
// @Success 200 {object} Response{data=models.Event}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /game/events [post]
func (h *GameHandler) CreateEvent(c *gin.Context) {
	var req CreateEventRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 设置默认值
	if req.Priority == 0 {
		req.Priority = 5
	}
	if req.EventData == nil {
		req.EventData = make(map[string]interface{})
	}

	event, err := h.eventService.CreateEvent(
		c.Request.Context(),
		req.WorldID,
		req.EventType,
		req.Name,
		req.Description,
		req.Priority,
		req.Participants,
		req.EventData,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "创建事件失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "创建事件成功",
		Data:    event,
	})
}

// ProcessEvent 处理事件
// @Summary 处理事件
// @Description 处理指定的事件
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param event_id path string true "事件ID"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /game/events/{event_id}/process [post]
func (h *GameHandler) ProcessEvent(c *gin.Context) {
	eventIDStr := c.Param("event_id")
	eventID, err := uuid.Parse(eventIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的事件ID格式",
		})
		return
	}

	if err := h.eventService.ProcessEvent(c.Request.Context(), eventID); err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "处理事件失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "事件处理成功",
	})
}

// PerformAction 执行角色行动
// @Summary 执行角色行动
// @Description 角色执行指定的行动，如移动、交互、说话等
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param character_id path string true "角色ID"
// @Param request body PerformActionRequest true "行动请求"
// @Success 200 {object} Response{data=ActionResult}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 403 {object} Response
// @Failure 500 {object} Response
// @Router /game/characters/{character_id}/actions [post]
func (h *GameHandler) PerformAction(c *gin.Context) {
	characterIDStr := c.Param("character_id")
	characterID, err := uuid.Parse(characterIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的角色ID格式",
		})
		return
	}

	var req PerformActionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	// 创建行动事件
	eventData := map[string]interface{}{
		"action_type":   req.ActionType,
		"character_id":  characterID.String(),
		"target_type":   req.TargetType,
		"target_id":     req.TargetID,
		"parameters":    req.Parameters,
		"user_id":       userID.String(),
	}

	event, err := h.eventService.CreateEvent(
		c.Request.Context(),
		req.WorldID,
		"character_action",
		fmt.Sprintf("角色行动: %s", req.ActionType),
		fmt.Sprintf("角色 %s 执行 %s 行动", characterID, req.ActionType),
		5, // 中等优先级
		[]uuid.UUID{characterID},
		eventData,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "创建行动事件失败",
			Error:   err.Error(),
		})
		return
	}

	// 立即处理事件
	if err := h.eventService.ProcessEvent(c.Request.Context(), event.ID); err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "执行行动失败",
			Error:   err.Error(),
		})
		return
	}

	// 构建行动结果
	result := ActionResult{
		EventID:     event.ID,
		ActionType:  req.ActionType,
		Success:     true,
		Message:     fmt.Sprintf("成功执行%s行动", req.ActionType),
		Effects:     map[string]interface{}{},
		Timestamp:   event.CreatedAt,
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "行动执行成功",
		Data:    result,
	})
}

// InteractWithCharacter 与角色交互
// @Summary 与角色交互
// @Description 角色与另一个角色进行交互
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param character_id path string true "角色ID"
// @Param target_character_id path string true "目标角色ID"
// @Param request body InteractionRequest true "交互请求"
// @Success 200 {object} Response{data=InteractionResult}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 403 {object} Response
// @Failure 500 {object} Response
// @Router /game/characters/{character_id}/interact/{target_character_id} [post]
func (h *GameHandler) InteractWithCharacter(c *gin.Context) {
	characterIDStr := c.Param("character_id")
	characterID, err := uuid.Parse(characterIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的角色ID格式",
		})
		return
	}

	targetCharacterIDStr := c.Param("target_character_id")
	targetCharacterID, err := uuid.Parse(targetCharacterIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的目标角色ID格式",
		})
		return
	}

	var req InteractionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	// 创建交互事件
	eventData := map[string]interface{}{
		"action_type":       "interact",
		"character_id":      characterID.String(),
		"target_type":       "character",
		"target_id":         targetCharacterID.String(),
		"interaction_type":  req.InteractionType,
		"content":           req.Content,
		"parameters":        req.Parameters,
		"user_id":           userID.String(),
	}

	event, err := h.eventService.CreateEvent(
		c.Request.Context(),
		req.WorldID,
		"character_action",
		fmt.Sprintf("角色交互: %s", req.InteractionType),
		fmt.Sprintf("角色 %s 与 %s 进行 %s 交互", characterID, targetCharacterID, req.InteractionType),
		5,
		[]uuid.UUID{characterID, targetCharacterID},
		eventData,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "创建交互事件失败",
			Error:   err.Error(),
		})
		return
	}

	// 处理交互事件
	if err := h.eventService.ProcessEvent(c.Request.Context(), event.ID); err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "执行交互失败",
			Error:   err.Error(),
		})
		return
	}

	// 构建交互结果
	result := InteractionResult{
		EventID:         event.ID,
		InteractionType: req.InteractionType,
		Success:         true,
		Message:         fmt.Sprintf("成功与角色进行%s交互", req.InteractionType),
		Response:        "", // 这里可以根据交互类型生成响应
		Effects:         map[string]interface{}{},
		Timestamp:       event.CreatedAt,
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "交互执行成功",
		Data:    result,
	})
}

// SpeakInScene 在场景中说话
// @Summary 在场景中说话
// @Description 角色在当前场景中说话，其他角色可以听到
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param character_id path string true "角色ID"
// @Param request body SpeakRequest true "说话请求"
// @Success 200 {object} Response{data=SpeechResult}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 403 {object} Response
// @Failure 500 {object} Response
// @Router /game/characters/{character_id}/speak [post]
func (h *GameHandler) SpeakInScene(c *gin.Context) {
	characterIDStr := c.Param("character_id")
	characterID, err := uuid.Parse(characterIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的角色ID格式",
		})
		return
	}

	var req SpeakRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	// 创建说话事件
	eventData := map[string]interface{}{
		"action_type": "speak",
		"character_id": characterID.String(),
		"content": req.Content,
		"speech_type": req.SpeechType,
		"target_character_id": req.TargetCharacterID,
		"volume": req.Volume,
		"emotion": req.Emotion,
		"user_id": userID.String(),
	}

	event, err := h.eventService.CreateEvent(
		c.Request.Context(),
		req.WorldID,
		"character_action",
		"角色说话",
		fmt.Sprintf("角色 %s 说话: %s", characterID, req.Content),
		3, // 较低优先级
		[]uuid.UUID{characterID},
		eventData,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "创建说话事件失败",
			Error:   err.Error(),
		})
		return
	}

	// 处理说话事件
	if err := h.eventService.ProcessEvent(c.Request.Context(), event.ID); err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "执行说话失败",
			Error:   err.Error(),
		})
		return
	}

	// 构建说话结果
	result := SpeechResult{
		EventID:     event.ID,
		Content:     req.Content,
		SpeechType:  req.SpeechType,
		Volume:      req.Volume,
		Emotion:     req.Emotion,
		Listeners:   []uuid.UUID{}, // 这里可以查询当前场景中的其他角色
		Success:     true,
		Message:     "说话成功",
		Timestamp:   event.CreatedAt,
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "说话成功",
		Data:    result,
	})
}

// TriggerEvent 触发事件
// @Summary 触发事件
// @Description 手动触发一个游戏事件
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body TriggerEventRequest true "触发事件请求"
// @Success 200 {object} Response{data=EventResult}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /game/events/trigger [post]
func (h *GameHandler) TriggerEvent(c *gin.Context) {
	var req TriggerEventRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	// 添加用户ID到事件数据
	if req.EventData == nil {
		req.EventData = make(map[string]interface{})
	}
	req.EventData["triggered_by_user"] = userID.String()

	// 创建事件
	event, err := h.eventService.CreateEvent(
		c.Request.Context(),
		req.WorldID,
		req.EventType,
		req.Name,
		req.Description,
		req.Priority,
		req.Participants,
		req.EventData,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "创建事件失败",
			Error:   err.Error(),
		})
		return
	}

	// 如果需要立即处理事件
	if req.ProcessImmediately {
		if err := h.eventService.ProcessEvent(c.Request.Context(), event.ID); err != nil {
			c.JSON(http.StatusInternalServerError, Response{
				Success: false,
				Message: "处理事件失败",
				Error:   err.Error(),
			})
			return
		}
	}

	// 构建事件结果
	result := EventResult{
		EventID:     event.ID,
		EventType:   req.EventType,
		Name:        req.Name,
		Status:      event.Status,
		Success:     true,
		Message:     "事件创建成功",
		Effects:     map[string]interface{}{},
		Timestamp:   event.CreatedAt,
	}

	if req.ProcessImmediately {
		result.Message = "事件创建并处理成功"
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: result.Message,
		Data:    result,
	})
}

// GetWorldState 获取世界状态
// @Summary 获取世界状态
// @Description 获取指定世界的当前状态信息
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param world_id path string true "世界ID"
// @Success 200 {object} Response{data=game.GameState}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /game/worlds/{world_id}/state [get]
func (h *GameHandler) GetWorldState(c *gin.Context) {
	worldIDStr := c.Param("world_id")
	worldID, err := uuid.Parse(worldIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的世界ID格式",
		})
		return
	}

	// 获取当前用户ID
	_, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	state, err := h.stateService.GetWorldState(c.Request.Context(), worldID)
	if err != nil {
		if err.Error() == "世界不存在" {
			c.JSON(http.StatusNotFound, Response{
				Success: false,
				Message: "世界不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取世界状态失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取世界状态成功",
		Data:    state,
	})
}

// UpdateWorldTime 更新世界时间
// @Summary 更新世界时间
// @Description 推进世界的游戏时间
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param world_id path string true "世界ID"
// @Param request body UpdateTimeRequest true "时间更新请求"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /game/worlds/{world_id}/time [post]
func (h *GameHandler) UpdateWorldTime(c *gin.Context) {
	worldIDStr := c.Param("world_id")
	worldID, err := uuid.Parse(worldIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的世界ID格式",
		})
		return
	}

	var req UpdateTimeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	_, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	if err := h.stateService.UpdateWorldTime(c.Request.Context(), worldID, req.Minutes); err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "更新世界时间失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: fmt.Sprintf("成功推进世界时间 %d 分钟", req.Minutes),
	})
}

// ProcessWorldTick 处理世界时钟周期
// @Summary 处理世界时钟周期
// @Description 执行世界的一个时钟周期，包括时间推进、NPC行为、环境事件等
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param world_id path string true "世界ID"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /game/worlds/{world_id}/tick [post]
func (h *GameHandler) ProcessWorldTick(c *gin.Context) {
	worldIDStr := c.Param("world_id")
	worldID, err := uuid.Parse(worldIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的世界ID格式",
		})
		return
	}

	// 获取当前用户ID
	_, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	if err := h.stateService.ProcessWorldTick(c.Request.Context(), worldID); err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "处理世界时钟周期失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "世界时钟周期处理成功",
	})
}

// 请求结构体定义

// MoveCharacterRequest 移动角色请求
type MoveCharacterRequest struct {
	SceneID uuid.UUID `json:"scene_id" binding:"required"`
}

// CreateSceneRequest 创建场景请求
type CreateSceneRequest struct {
	WorldID     uuid.UUID              `json:"world_id" binding:"required"`
	Name        string                 `json:"name" binding:"required"`
	Description string                 `json:"description"`
	SceneType   string                 `json:"scene_type" binding:"required"`
	Properties  map[string]interface{} `json:"properties"`
}

// CreateEventRequest 创建事件请求
type CreateEventRequest struct {
	WorldID      uuid.UUID              `json:"world_id" binding:"required"`
	EventType    string                 `json:"event_type" binding:"required"`
	Name         string                 `json:"name" binding:"required"`
	Description  string                 `json:"description"`
	Priority     int                    `json:"priority"`
	Participants []uuid.UUID            `json:"participants"`
	EventData    map[string]interface{} `json:"event_data"`
}

// UpdateWorld 更新世界信息
// @Summary 更新世界信息
// @Description 更新指定世界的信息
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param world_id path string true "世界ID"
// @Param request body UpdateWorldRequest true "更新世界请求"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 403 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /game/worlds/{world_id} [put]
func (h *GameHandler) UpdateWorld(c *gin.Context) {
	worldIDStr := c.Param("world_id")
	worldID, err := uuid.Parse(worldIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的世界ID格式",
		})
		return
	}

	var req UpdateWorldRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	// 构建更新字段
	updates := make(map[string]interface{})
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Description != nil {
		updates["description"] = req.Description
	}
	if req.IsPublic != nil {
		updates["is_public"] = *req.IsPublic
	}
	if req.MaxPlayers != nil {
		updates["max_players"] = *req.MaxPlayers
	}
	if req.Status != "" {
		updates["status"] = req.Status
	}

	// 验证用户权限（只有创建者可以更新）
	world, err := h.worldService.GetWorld(c.Request.Context(), worldID)
	if err != nil {
		if err.Error() == "世界不存在" {
			c.JSON(http.StatusNotFound, Response{
				Success: false,
				Message: "世界不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取世界信息失败",
			Error:   err.Error(),
		})
		return
	}

	if world.CreatorID != userID {
		c.JSON(http.StatusForbidden, Response{
			Success: false,
			Message: "无权限修改此世界",
		})
		return
	}

	if err := h.worldService.UpdateWorld(c.Request.Context(), worldID, updates); err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "更新世界失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "更新世界成功",
	})
}

// DeleteWorld 删除世界
// @Summary 删除世界
// @Description 删除指定的世界
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param world_id path string true "世界ID"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 403 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /game/worlds/{world_id} [delete]
func (h *GameHandler) DeleteWorld(c *gin.Context) {
	worldIDStr := c.Param("world_id")
	worldID, err := uuid.Parse(worldIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的世界ID格式",
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	if err := h.worldService.DeleteWorld(c.Request.Context(), worldID, userID); err != nil {
		if err.Error() == "世界不存在或无权限删除" {
			c.JSON(http.StatusForbidden, Response{
				Success: false,
				Message: "世界不存在或无权限删除",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "删除世界失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "删除世界成功",
	})
}

// UpdateWorldRequest 更新世界请求
type UpdateWorldRequest struct {
	Name        string  `json:"name,omitempty"`
	Description *string `json:"description,omitempty"`
	IsPublic    *bool   `json:"is_public,omitempty"`
	MaxPlayers  *int    `json:"max_players,omitempty"`
	Status      string  `json:"status,omitempty"`
}

// PaginatedResponse 分页响应
type PaginatedResponse struct {
	Items      interface{} `json:"items"`
	Total      int64       `json:"total"`
	Page       int         `json:"page"`
	Limit      int         `json:"limit"`
	TotalPages int64       `json:"total_pages"`
}
