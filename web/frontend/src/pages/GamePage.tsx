import React, { useEffect, useState } from 'react'
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom'
import {
  Card,
  Typography,
  Input,
  Button,
  List,
  Avatar,
  Space,
  Spin,
  message,
  Select,
  Divider,
  Tag
} from 'antd'
import {
  SendOutlined,
  UserOutlined,
  RobotOutlined,
  EnvironmentOutlined,
  ClockCircleOutlined,
  TeamOutlined
} from '@ant-design/icons'
import styled from 'styled-components'
import { useAppDispatch } from '../store/hooks'
import { setPageTitle } from '../store/slices/uiSlice'
import {
  useGetWorldQuery,
  useGetWorldCharactersQuery,
  useGetMyCharactersQuery,
  usePerformActionMutation,
  useSpeakInSceneMutation,

} from '../store/api'
import type { GameCharacter } from '../store/slices/gameSlice'
import CharacterSelectionModal from '../components/CharacterSelectionModal'


const { Title, Text, Paragraph } = Typography
const { TextArea } = Input
const { Option } = Select

const Container = styled.div`
  min-height: calc(100vh - 64px - 70px);
  padding: 16px;
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
`

const GameLayout = styled.div`
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 16px;
  height: calc(100vh - 64px - 70px - 32px);

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr auto;
  }
`

const MainPanel = styled(Card)`
  height: 100%;
  display: flex;
  flex-direction: column;

  .ant-card-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 16px;
  }
`

const SidePanel = styled(Card)`
  height: 100%;

  .ant-card-body {
    padding: 16px;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
`

const ChatArea = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
`

const MessageList = styled.div`
  flex: 1;
  overflow-y: auto;
  margin-bottom: 16px;
  padding: 8px;
  background: #f8fafc;
  border-radius: 6px;
  min-height: 300px;
`

const InputArea = styled.div`
  border-top: 1px solid #e2e8f0;
  padding-top: 16px;
`

const CharacterList = styled.div`
  flex: 1;
  overflow-y: auto;
`

interface GameMessage {
  id: string
  type: 'speech' | 'action' | 'system'
  characterId?: string
  characterName?: string
  content: string
  timestamp: Date
}

const GamePage: React.FC = () => {
  const { worldId } = useParams<{ worldId: string }>()
  const navigate = useNavigate()
  const dispatch = useAppDispatch()


  const [selectedCharacter, setSelectedCharacter] = useState<string>('')
  const [inputText, setInputText] = useState('')
  const [actionType, setActionType] = useState<'speech' | 'action'>('speech')
  const [messages, setMessages] = useState<GameMessage[]>([])
  const [loading, setLoading] = useState(false)

  // 角色选择模态框状态
  const [characterSelectionVisible, setCharacterSelectionVisible] = useState(false)

  // API hooks
  const { data: world, isLoading: worldLoading, error: worldError } = useGetWorldQuery(worldId!, {
    skip: !worldId
  })

  const { data: worldCharacters, isLoading: charactersLoading } = useGetWorldCharactersQuery({
    worldId: worldId!
  }, {
    skip: !worldId
  })

  const { data: myCharacters } = useGetMyCharactersQuery({})

  const [performAction] = usePerformActionMutation()
  const [speakInScene] = useSpeakInSceneMutation()

  useEffect(() => {
    if (world?.data) {
      dispatch(setPageTitle(`游戏中 - ${world.data.name}`))
    }
  }, [dispatch, world])

  useEffect(() => {
    if (worldError) {
      message.error('加载世界失败')
      navigate('/lobby')
    }
  }, [worldError, navigate])

  // 选择默认角色
  useEffect(() => {
    if (myCharacters?.data?.items && myCharacters.data.items.length > 0 && !selectedCharacter) {
      const worldCharacter = myCharacters.data.items.find(char => char.worldId === worldId)
      if (worldCharacter) {
        setSelectedCharacter(worldCharacter.id)
      }
    }
  }, [myCharacters, worldId, selectedCharacter])

  // 发送消息/执行动作
  const handleSendMessage = async () => {
    if (!inputText.trim() || !selectedCharacter) {
      message.warning('请输入内容并选择角色')
      return
    }

    setLoading(true)
    try {
      const newMessage: GameMessage = {
        id: Date.now().toString(),
        type: actionType,
        characterId: selectedCharacter,
        characterName: myCharacters?.data?.items?.find(c => c.id === selectedCharacter)?.name || '未知角色',
        content: inputText,
        timestamp: new Date()
      }

      // 添加到消息列表
      setMessages(prev => [...prev, newMessage])

      if (actionType === 'speech') {
        await speakInScene({
          characterId: selectedCharacter,
          data: {
            world_id: worldId!,
            content: inputText,
            speech_type: 'say',
            volume: 'normal',
            emotion: 'neutral'
          }
        }).unwrap()
      } else {
        await performAction({
          characterId: selectedCharacter,
          data: {
            world_id: worldId!,
            action_type: 'explore',
            parameters: {
              description: inputText
            }
          }
        }).unwrap()
      }

      setInputText('')
      message.success(actionType === 'speech' ? '发言成功' : '动作执行成功')
    } catch (error: any) {
      console.error('操作失败:', error)
      message.error(error?.data?.message || '操作失败')
      // 移除失败的消息
      setMessages(prev => prev.filter(msg => msg.id !== Date.now().toString()))
    } finally {
      setLoading(false)
    }
  }

  if (worldLoading) {
    return (
      <Container>
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
          <Spin size="large" />
        </div>
      </Container>
    )
  }

  if (!world?.data) {
    return (
      <Container>
        <Card style={{ textAlign: 'center' }}>
          <Title level={3}>世界不存在</Title>
          <Button type="primary" onClick={() => navigate('/lobby')}>
            返回大厅
          </Button>
        </Card>
      </Container>
    )
  }

  const myWorldCharacters = myCharacters?.data?.items?.filter(char => char.worldId === worldId) || []

  // 处理角色选择成功
  const handleCharacterSelected = (character: GameCharacter) => {
    setSelectedCharacter(character.id)
    message.success(`成功选择角色：${character.name}`)
    // 刷新我的角色列表
    // 这里可以触发重新获取数据或者手动更新状态
  }

  return (
    <Container>
      <GameLayout>
        <MainPanel title={
          <Space>
            <EnvironmentOutlined />
            <span>{world.data.name}</span>
            <Tag color="blue">{world.data.theme}</Tag>
          </Space>
        }>
          <ChatArea>
            <div style={{ marginBottom: 16 }}>
              <Paragraph ellipsis={{ rows: 2 }}>
                <Text type="secondary">{world.data.description}</Text>
              </Paragraph>
            </div>

            <MessageList>
              {messages.length === 0 ? (
                <div style={{ textAlign: 'center', color: '#94a3b8', marginTop: 50 }}>
                  <RobotOutlined style={{ fontSize: 48, marginBottom: 16 }} />
                  <div>欢迎来到 {world.data.name}！</div>
                  <div>开始你的冒险吧...</div>
                </div>
              ) : (
                <List
                  dataSource={messages}
                  renderItem={(message) => (
                    <List.Item style={{ border: 'none', padding: '8px 0' }}>
                      <List.Item.Meta
                        avatar={
                          <Avatar
                            icon={<UserOutlined />}
                            style={{
                              backgroundColor: message.type === 'action' ? '#52c41a' : '#1890ff'
                            }}
                          />
                        }
                        title={
                          <Space>
                            <span>{message.characterName}</span>
                            <Tag color={message.type === 'action' ? 'green' : 'blue'}>
                              {message.type === 'action' ? '动作' : '发言'}
                            </Tag>
                            <Text type="secondary" style={{ fontSize: 12 }}>
                              <ClockCircleOutlined style={{ marginRight: 4 }} />
                              {message.timestamp.toLocaleTimeString()}
                            </Text>
                          </Space>
                        }
                        description={message.content}
                      />
                    </List.Item>
                  )}
                />
              )}
            </MessageList>

            <InputArea>
              <Space.Compact style={{ width: '100%', marginBottom: 8 }}>
                <Select
                  value={selectedCharacter}
                  onChange={setSelectedCharacter}
                  placeholder="选择角色"
                  style={{ width: 150 }}
                  disabled={myWorldCharacters.length === 0}
                >
                  {myWorldCharacters.map(character => (
                    <Option key={character.id} value={character.id}>
                      {character.name}
                    </Option>
                  ))}
                </Select>
                <Select
                  value={actionType}
                  onChange={setActionType}
                  style={{ width: 100 }}
                >
                  <Option value="speech">发言</Option>
                  <Option value="action">动作</Option>
                </Select>
              </Space.Compact>

              <Space.Compact style={{ width: '100%' }}>
                <TextArea
                  value={inputText}
                  onChange={(e) => setInputText(e.target.value)}
                  placeholder={actionType === 'speech' ? '说些什么...' : '描述你的动作...'}
                  autoSize={{ minRows: 2, maxRows: 4 }}
                  onPressEnter={(e) => {
                    if (e.shiftKey) return
                    e.preventDefault()
                    handleSendMessage()
                  }}
                />
                <Button
                  type="primary"
                  icon={<SendOutlined />}
                  loading={loading}
                  onClick={handleSendMessage}
                  disabled={!inputText.trim() || !selectedCharacter}
                >
                  发送
                </Button>
              </Space.Compact>

              <Text type="secondary" style={{ fontSize: 12, marginTop: 4, display: 'block' }}>
                按 Enter 发送，Shift + Enter 换行
              </Text>
            </InputArea>
          </ChatArea>
        </MainPanel>

        <SidePanel title={
          <Space>
            <TeamOutlined />
            <span>角色列表</span>
          </Space>
        }>
          <CharacterList>
            {charactersLoading ? (
              <div style={{ textAlign: 'center', padding: 20 }}>
                <Spin />
              </div>
            ) : (
              <>
                <Divider orientation="left" style={{ margin: '8px 0' }}>我的角色</Divider>
                <List
                  size="small"
                  dataSource={myWorldCharacters}
                  renderItem={(character) => (
                    <List.Item style={{ padding: '8px 0' }}>
                      <List.Item.Meta
                        avatar={<Avatar icon={<UserOutlined />} />}
                        title={character.name}
                        description={
                          <Text type="secondary" ellipsis>
                            {character.description}
                          </Text>
                        }
                      />
                    </List.Item>
                  )}
                />

                {worldCharacters?.data?.items && worldCharacters.data.items.length > myWorldCharacters.length && (
                  <>
                    <Divider orientation="left" style={{ margin: '16px 0 8px 0' }}>其他角色</Divider>
                    <List
                      size="small"
                      dataSource={worldCharacters.data.items.filter(char =>
                        !myWorldCharacters.some(myChar => myChar.id === char.id)
                      )}
                      renderItem={(character) => (
                        <List.Item style={{ padding: '8px 0' }}>
                          <List.Item.Meta
                            avatar={<Avatar icon={<RobotOutlined />} />}
                            title={character.name}
                            description={
                              <Text type="secondary" ellipsis>
                                {character.description}
                              </Text>
                            }
                          />
                        </List.Item>
                      )}
                    />
                  </>
                )}
              </>
            )}

            {myWorldCharacters.length === 0 && (
              <div style={{ textAlign: 'center', padding: 20, color: '#94a3b8' }}>
                <UserOutlined style={{ fontSize: 32, marginBottom: 8 }} />
                <div>你还没有角色</div>
                <div style={{ fontSize: '12px', color: '#64748b', margin: '8px 0' }}>
                  从世界中的NPC角色中选择一个作为你的化身
                </div>
                <Button
                  type="primary"
                  size="small"
                  onClick={() => setCharacterSelectionVisible(true)}
                >
                  选择角色
                </Button>
              </div>
            )}
          </CharacterList>
        </SidePanel>
      </GameLayout>

      {/* 角色选择模态框 */}
      <CharacterSelectionModal
        world={world?.data || null}
        visible={characterSelectionVisible}
        onClose={() => setCharacterSelectionVisible(false)}
        onCharacterSelected={handleCharacterSelected}
      />
    </Container>
  )
}

export default GamePage
